import type { Metadata } from "next";
import { Mail, Download } from "lucide-react";
import ComingSoonCard from "@/components/admin/ComingSoonCard";

export const metadata: Metadata = {
  title: "Subscriptions | Admin Panel",
  description: "Manage email subscriptions and newsletters",
};

export default function SubscriptionsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-light text-gray-900 dark:text-white">
            Email Subscriptions
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Manage newsletter subscribers and email campaigns
          </p>
        </div>
        <button className="flex items-center space-x-2 bg-black dark:bg-white text-white dark:text-black px-4 py-2 rounded-lg hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors">
          <Download className="w-4 h-4" />
          <span>Export List</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Subscribers
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white mt-2">
                1,234
              </p>
            </div>
            <Mail className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                This Week
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white mt-2">
                45
              </p>
            </div>
            <Mail className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Growth Rate
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white mt-2">
                +12%
              </p>
            </div>
            <Mail className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Coming Soon Notice */}
      <ComingSoonCard
        icon={Mail}
        title="Email Management Coming Soon"
        description="Full subscriber management, email campaigns, templates, analytics, and automated sequences are being developed."
      />
    </div>
  );
}
