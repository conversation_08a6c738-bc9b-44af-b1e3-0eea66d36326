import type { Metadata } from "next";
import { BarChart3, Users, Calendar, Mail, TrendingUp, Clock } from "lucide-react";

export const metadata: Metadata = {
  title: "Dashboard | Admin Panel",
  description: "Admin dashboard overview",
};

const stats = [
  {
    name: "Total Users",
    value: "1,234",
    change: "+12%",
    changeType: "positive" as const,
    icon: Users,
  },
  {
    name: "Appointments Today",
    value: "23",
    change: "+5%",
    changeType: "positive" as const,
    icon: Calendar,
  },
  {
    name: "Email Subscribers",
    value: "567",
    change: "+18%",
    changeType: "positive" as const,
    icon: Mail,
  },
  {
    name: "Revenue This Month",
    value: "$12,345",
    change: "+8%",
    changeType: "positive" as const,
    icon: TrendingUp,
  },
];

const recentActivity = [
  {
    id: 1,
    type: "appointment",
    message: "New appointment booked by <PERSON>",
    time: "2 minutes ago",
  },
  {
    id: 2,
    type: "user",
    message: "New user registration: <EMAIL>",
    time: "15 minutes ago",
  },
  {
    id: 3,
    type: "subscription",
    message: "New email subscription: <EMAIL>",
    time: "1 hour ago",
  },
  {
    id: 4,
    type: "appointment",
    message: "Appointment completed by <PERSON>",
    time: "2 hours ago",
  },
];

export default function AdminDashboardPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-light text-gray-900 dark:text-white">
          Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Welcome back! Here's what's happening with your barber booking system.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div
              key={stat.name}
              className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.name}
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white mt-2">
                    {stat.value}
                  </p>
                </div>
                <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                  <Icon className="w-6 h-6 text-gray-600 dark:text-gray-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <span className="text-sm font-medium text-green-600 dark:text-green-400">
                  {stat.change}
                </span>
                <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
                  from last month
                </span>
              </div>
            </div>
          );
        })}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Recent Activity
            </h2>
            <Clock className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900 dark:text-white">
                    {activity.message}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {activity.time}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
            Quick Actions
          </h2>
          <div className="space-y-3">
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <div className="font-medium text-gray-900 dark:text-white">
                Add New Barber
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Register a new barber in the system
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <div className="font-medium text-gray-900 dark:text-white">
                View Appointments
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Manage today's appointments
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <div className="font-medium text-gray-900 dark:text-white">
                Send Promotion
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Create and send promotional emails
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Coming Soon Notice */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        <div className="flex items-center space-x-3">
          <BarChart3 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          <div>
            <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100">
              Full Dashboard Coming Soon
            </h3>
            <p className="text-blue-700 dark:text-blue-300 mt-1">
              Advanced analytics, detailed reports, and more features are being developed.
              This is a preview of the admin panel structure.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
