import type { Metadata } from "next";
import { Calendar, Plus, Search, Clock } from "lucide-react";
import ComingSoonCard from "@/components/admin/ComingSoonCard";

export const metadata: Metadata = {
  title: "Appointments | Admin Panel",
  description: "Manage appointments and bookings",
};

const mockAppointments = [
  {
    id: 1,
    customerName: "John Doe",
    barberName: "<PERSON> Johnson",
    service: "Classic Haircut",
    date: "2024-01-20",
    time: "10:00 AM",
    status: "Confirmed",
    duration: "30 min",
  },
  {
    id: 2,
    customerName: "<PERSON>",
    barberName: "<PERSON>",
    service: "Beard Trim",
    date: "2024-01-20",
    time: "11:30 AM",
    status: "Pending",
    duration: "20 min",
  },
  {
    id: 3,
    customerName: "<PERSON>",
    barberName: "<PERSON>",
    service: "Full Service",
    date: "2024-01-20",
    time: "2:00 PM",
    status: "Completed",
    duration: "60 min",
  },
];

export default function AppointmentsPage() {
  return (
    <div className="space-y-6">
      {/* Page <PERSON>er */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-light text-gray-900 dark:text-white">
            Appointments
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Manage bookings and scheduling
          </p>
        </div>
        <button className="flex items-center space-x-2 bg-black dark:bg-white text-white dark:text-black px-4 py-2 rounded-lg hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors">
          <Plus className="w-4 h-4" />
          <span>New Appointment</span>
        </button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search appointments..."
            className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-800 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:border-black dark:focus:border-white focus:outline-none"
          />
        </div>
        <select className="px-4 py-2 border border-gray-200 dark:border-gray-800 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:border-black dark:focus:border-white focus:outline-none">
          <option value="">All Status</option>
          <option value="confirmed">Confirmed</option>
          <option value="pending">Pending</option>
          <option value="completed">Completed</option>
          <option value="cancelled">Cancelled</option>
        </select>
        <input
          type="date"
          className="px-4 py-2 border border-gray-200 dark:border-gray-800 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:border-black dark:focus:border-white focus:outline-none"
        />
      </div>

      {/* Appointments Table */}
      <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <tr>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-900 dark:text-white">
                  Customer
                </th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-900 dark:text-white">
                  Barber
                </th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-900 dark:text-white">
                  Service
                </th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-900 dark:text-white">
                  Date & Time
                </th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-900 dark:text-white">
                  Status
                </th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-900 dark:text-white">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {mockAppointments.map((appointment) => (
                <tr key={appointment.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td className="py-4 px-6">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {appointment.customerName}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      ID: {appointment.id}
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {appointment.barberName}
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {appointment.service}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {appointment.duration}
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {new Date(appointment.date).toLocaleDateString()}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {appointment.time}
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <span
                      className={`inline-block px-2 py-1 text-xs rounded-full ${
                        appointment.status === "Confirmed"
                          ? "bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200"
                          : appointment.status === "Pending"
                          ? "bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200"
                          : appointment.status === "Completed"
                          ? "bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200"
                          : "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200"
                      }`}
                    >
                      {appointment.status}
                    </span>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <button className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm">
                        Edit
                      </button>
                      <button className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 text-sm">
                        Complete
                      </button>
                      <button className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm">
                        Cancel
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Coming Soon Notice */}
      <ComingSoonCard
        icon={Calendar}
        title="Advanced Scheduling Coming Soon"
        description="Calendar view, automated reminders, recurring appointments, and real-time availability are being developed."
      />
    </div>
  );
}
