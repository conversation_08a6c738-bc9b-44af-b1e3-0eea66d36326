import type { Metadata } from "next";
import { Scissors, Plus, Search } from "lucide-react";

export const metadata: Metadata = {
  title: "Barbers | Admin Panel",
  description: "Manage barbers in the system",
};

const mockBarbers = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    specialties: ["Classic Cuts", "Beard Trimming"],
    status: "Active",
    joinDate: "2024-01-15",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    specialties: ["Modern Styles", "Hair Washing"],
    status: "Active",
    joinDate: "2024-02-20",
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    specialties: ["Fade Cuts", "Styling"],
    status: "Inactive",
    joinDate: "2023-12-10",
  },
];

export default function BarbersPage() {
  return (
    <div className="space-y-6">
      {/* <PERSON> */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-light text-gray-900 dark:text-white">
            Barbers
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Manage barbers and their information
          </p>
        </div>
        <button className="flex items-center space-x-2 bg-black dark:bg-white text-white dark:text-black px-4 py-2 rounded-lg hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors">
          <Plus className="w-4 h-4" />
          <span>Add Barber</span>
        </button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search barbers..."
            className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-800 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:border-black dark:focus:border-white focus:outline-none"
          />
        </div>
        <select className="px-4 py-2 border border-gray-200 dark:border-gray-800 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:border-black dark:focus:border-white focus:outline-none">
          <option value="">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      {/* Barbers Table */}
      <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <tr>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-900 dark:text-white">
                  Barber
                </th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-900 dark:text-white">
                  Contact
                </th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-900 dark:text-white">
                  Specialties
                </th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-900 dark:text-white">
                  Status
                </th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-900 dark:text-white">
                  Join Date
                </th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-900 dark:text-white">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {mockBarbers.map((barber) => (
                <tr key={barber.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <Scissors className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {barber.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          ID: {barber.id}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {barber.email}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {barber.phone}
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex flex-wrap gap-1">
                      {barber.specialties.map((specialty, index) => (
                        <span
                          key={index}
                          className="inline-block px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <span
                      className={`inline-block px-2 py-1 text-xs rounded-full ${
                        barber.status === "Active"
                          ? "bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200"
                          : "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200"
                      }`}
                    >
                      {barber.status}
                    </span>
                  </td>
                  <td className="py-4 px-6 text-sm text-gray-900 dark:text-white">
                    {new Date(barber.joinDate).toLocaleDateString()}
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <button className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm">
                        Edit
                      </button>
                      <button className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm">
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Coming Soon Notice */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        <div className="flex items-center space-x-3">
          <Scissors className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          <div>
            <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100">
              Barber Management Coming Soon
            </h3>
            <p className="text-blue-700 dark:text-blue-300 mt-1">
              Full CRUD operations, barber profiles, scheduling, and performance analytics are being developed.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
