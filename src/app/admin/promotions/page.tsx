import type { Metadata } from "next";
import { Megaphone, Plus } from "lucide-react";
import ComingSoonCard from "@/components/admin/ComingSoonCard";

export const metadata: Metadata = {
  title: "Promotions | Admin Panel",
  description: "Manage promotional campaigns and offers",
};

export default function PromotionsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-light text-gray-900 dark:text-white">
            Promotions
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Create and manage promotional campaigns
          </p>
        </div>
        <button className="flex items-center space-x-2 bg-black dark:bg-white text-white dark:text-black px-4 py-2 rounded-lg hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors">
          <Plus className="w-4 h-4" />
          <span>New Promotion</span>
        </button>
      </div>

      {/* Placeholder Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Active Campaigns
          </h3>
          <p className="text-3xl font-bold text-green-600 dark:text-green-400">3</p>
        </div>
        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Draft Campaigns
          </h3>
          <p className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">2</p>
        </div>
        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Total Reach
          </h3>
          <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">1.2K</p>
        </div>
      </div>

      {/* Coming Soon Notice */}
      <ComingSoonCard
        icon={Megaphone}
        title="Promotion Management Coming Soon"
        description="Campaign builder, discount codes, targeted promotions, A/B testing, and performance analytics are being developed."
      />
    </div>
  );
}
