import type { Metadata } from "next";
import { BarChart3, TrendingUp, Users, Calendar } from "lucide-react";
import ComingSoonCard from "@/components/admin/ComingSoonCard";

export const metadata: Metadata = {
  title: "Analytics | Admin Panel",
  description: "View analytics and reports",
};

export default function AnalyticsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-light text-gray-900 dark:text-white">
          Analytics
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Track performance and insights
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Monthly Revenue
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white mt-2">
                $12,345
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-500" />
          </div>
          <div className="mt-4">
            <span className="text-sm font-medium text-green-600 dark:text-green-400">
              +8.2%
            </span>
            <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
              from last month
            </span>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Bookings
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white mt-2">
                456
              </p>
            </div>
            <Calendar className="w-8 h-8 text-blue-500" />
          </div>
          <div className="mt-4">
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
              +12.5%
            </span>
            <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
              from last month
            </span>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                New Customers
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white mt-2">
                89
              </p>
            </div>
            <Users className="w-8 h-8 text-purple-500" />
          </div>
          <div className="mt-4">
            <span className="text-sm font-medium text-purple-600 dark:text-purple-400">
              +15.3%
            </span>
            <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
              from last month
            </span>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Avg. Rating
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white mt-2">
                4.8
              </p>
            </div>
            <BarChart3 className="w-8 h-8 text-yellow-500" />
          </div>
          <div className="mt-4">
            <span className="text-sm font-medium text-yellow-600 dark:text-yellow-400">
              +0.2
            </span>
            <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
              from last month
            </span>
          </div>
        </div>
      </div>

      {/* Chart Placeholder */}
      <div className="bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Revenue Trend
        </h3>
        <div className="h-64 bg-gray-50 dark:bg-gray-800 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500 dark:text-gray-400">
              Chart visualization will be implemented here
            </p>
          </div>
        </div>
      </div>

      {/* Coming Soon Notice */}
      <ComingSoonCard
        icon={BarChart3}
        title="Advanced Analytics Coming Soon"
        description="Interactive charts, custom reports, revenue analytics, customer insights, and performance dashboards are being developed."
      />
    </div>
  );
}
