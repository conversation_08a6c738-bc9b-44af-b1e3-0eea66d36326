@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Enhanced animations for Coming Soon page */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gentle-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes gentle-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes float-1 {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-8px) rotate(180deg);
  }
}

@keyframes float-2 {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-6px) rotate(-180deg);
  }
}

@keyframes progress-line {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(100%);
  }
}

.animate-fade-in {
  animation: fade-in 1s ease-out;
}

.animate-slide-up {
  animation: slide-up 1.2s ease-out 0.2s both;
}

.animate-fade-in-delay {
  animation: fade-in 1s ease-out 0.4s both;
}

.animate-fade-in-delay-2 {
  animation: fade-in 1s ease-out 0.6s both;
}

.animate-fade-in-delay-3 {
  animation: fade-in 1s ease-out 0.8s both;
}

.animate-gentle-pulse {
  animation: gentle-pulse 3s ease-in-out infinite;
}

.animate-gentle-bounce {
  animation: gentle-bounce 2s ease-in-out infinite;
}

.animate-float-1 {
  animation: float-1 4s ease-in-out infinite;
}

.animate-float-2 {
  animation: float-2 5s ease-in-out infinite 1s;
}

.animate-progress-line {
  animation: progress-line 3s ease-in-out infinite;
}

/* Discovery Page Styles */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Mobile-first responsive design */
@media (max-width: 640px) {
  .discovery-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .discovery-card {
    border-radius: 12px;
  }

  .filter-pill {
    font-size: 14px;
    padding: 8px 16px;
  }
}

/* Horizontal scroll improvements */
.horizontal-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Ensure proper spacing for horizontal cards */
.horizontal-card {
  scroll-snap-align: start;
}

/* Custom scrollbar for web view */
@media (min-width: 768px) {
  .scrollbar-hide::-webkit-scrollbar {
    height: 4px;
  }

  .scrollbar-hide::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  .scrollbar-hide::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
  }

  .scrollbar-hide::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
  }
}
