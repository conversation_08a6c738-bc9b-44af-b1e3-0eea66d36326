'use client';

import React from 'react';

const ComingSoon = () => {

  return (
    <div className="min-h-screen bg-white dark:bg-black flex items-center justify-center px-6 overflow-hidden">
      <div className="max-w-3xl mx-auto text-center">

        {/* Animated Icon */}
        <div className="flex justify-center mb-16 animate-fade-in">
          <div className="relative">
            <div className="w-20 h-20 bg-black dark:bg-white rounded-full flex items-center justify-center animate-gentle-pulse">
              <svg
                className="w-10 h-10 text-white dark:text-black animate-gentle-bounce"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M9.64 7.64c.23-.5.36-1.05.36-1.64 0-2.21-1.79-4-4-4S2 3.79 2 6s1.79 4 4 4c.59 0 1.14-.13 1.64-.36L10 12l-2.36 2.36C7.14 14.13 6.59 14 6 14c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4c0-.59-.13-1.14-.36-1.64L12 14l7 7h3v-1L9.64 7.64zM6 8c-1.1 0-2-.89-2-2s.89-2 2-2 2 .89 2 2-.89 2-2 2zm0 12c-1.1 0-2-.89-2-2s.89-2 2-2 2 .89 2 2-.89 2-2 2zm6-7.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.5.5.22.5.5-.22.5-.5.5zM19 3l-6 6 2 2 7-7V3h-3z"/>
              </svg>
            </div>
            {/* Floating dots */}
            <div className="absolute -top-2 -right-2 w-3 h-3 bg-black dark:bg-white rounded-full animate-float-1"></div>
            <div className="absolute -bottom-2 -left-2 w-2 h-2 bg-black dark:bg-white rounded-full animate-float-2"></div>
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-8 animate-slide-up">
          <div className="space-y-6">
            <h1 className="text-6xl md:text-7xl lg:text-8xl font-extralight text-black dark:text-white tracking-tighter leading-none">
              Something
              <br />
              <span className="font-light italic">Extraordinary</span>
              <br />
              <span className="text-4xl md:text-5xl lg:text-6xl font-thin text-gray-400 dark:text-gray-600">
                is coming
              </span>
            </h1>

            <div className="max-w-lg mx-auto space-y-4 animate-fade-in-delay">
              <p className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 font-light leading-relaxed">
                The future of barbering appointments.
              </p>
              <p className="text-base text-gray-500 dark:text-gray-500 font-light">
                Seamless booking • Premium experience • Crafted with precision
              </p>
            </div>
          </div>

          {/* Progress Animation */}
          <div className="space-y-4 animate-fade-in-delay-2">
            <div className="w-48 h-px bg-gray-100 dark:bg-gray-900 mx-auto overflow-hidden">
              <div className="h-full bg-black dark:bg-white animate-progress-line"></div>
            </div>
            <p className="text-sm text-gray-400 dark:text-gray-600 font-light tracking-wide">
              LAUNCHING SOON
            </p>
          </div>

          {/* Simple CTA */}
          <div className="pt-8 animate-fade-in-delay-3">
            <div className="text-center">
              <p className="text-lg text-gray-600 dark:text-gray-400 font-light">
                Stay tuned for something amazing
              </p>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default ComingSoon;
