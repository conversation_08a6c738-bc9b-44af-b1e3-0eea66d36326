'use client';

import React from 'react';
import { Search, SlidersHorizontal, MapPin } from 'lucide-react';

interface SearchHeaderProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export default function SearchHeader({ searchQuery, onSearchChange }: SearchHeaderProps) {
  return (
    <div className="px-4 pb-4">
      <div className="flex items-center space-x-3">
        {/* Search Input */}
        <div className="flex-1 relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search for barbers or services"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent text-sm"
          />
        </div>

        {/* Filter Button */}
        <button className="p-3 border border-gray-300 rounded-xl bg-white hover:bg-gray-50 transition-colors">
          <SlidersHorizontal className="h-5 w-5 text-gray-600" />
        </button>

        {/* Location Button */}
        <button className="p-3 border border-gray-300 rounded-xl bg-white hover:bg-gray-50 transition-colors">
          <MapPin className="h-5 w-5 text-gray-600" />
        </button>
      </div>
    </div>
  );
}
