'use client';

import React from 'react';
import { MapPin, Star, User } from 'lucide-react';
import { mockBarbers } from '@/data/mockBarbers';

export default function BarberGrid() {
  return (
    <div className="px-4 pb-6">
      {/* Section Title */}
      <div className="mb-4">
        <h2 className="text-lg font-medium text-gray-900">Nearby Barbers</h2>
      </div>

      {/* Horizontal Scrollable Cards */}
      <div className="flex space-x-3 md:space-x-4 overflow-x-auto scrollbar-hide horizontal-scroll">
        {mockBarbers.map((barber) => (
          <div key={barber.id} className="flex-shrink-0 w-[280px] sm:w-[300px] md:w-[320px] lg:w-[280px] bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-md transition-shadow horizontal-card">
            {/* Image Container */}
            <div className="relative h-48 md:h-52 bg-gray-100">
              {barber.isNew && (
                <div className="absolute top-3 left-3 z-10">
                  <span className="bg-white text-gray-600 text-xs font-medium px-2 py-1 rounded-full border">
                    NEW
                  </span>
                </div>
              )}

              {barber.image ? (
                <img
                  src={barber.image}
                  alt={barber.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <User className="w-12 h-12 md:w-16 md:h-16 text-gray-300" />
                </div>
              )}
            </div>

            {/* Content */}
            <div className="p-4">
              {/* Distance */}
              <p className="text-sm text-gray-500 mb-2">{barber.distance}</p>

              {/* Barber Name */}
              <h3 className="font-medium text-gray-900 mb-1 text-base">{barber.name}</h3>

              {/* Shop Name */}
              <p className="text-sm text-gray-600 mb-3">{barber.shop}</p>

              {/* Location */}
              <div className="flex items-center text-sm text-gray-500 mb-3">
                <MapPin className="w-4 h-4 mr-1" />
                <span>{barber.location}</span>
              </div>

              {/* Rating */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{barber.rating}</span>
                </div>
                <span className="text-sm text-gray-500">({barber.reviews})</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
