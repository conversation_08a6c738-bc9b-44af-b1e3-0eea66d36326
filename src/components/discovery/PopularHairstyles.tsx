'use client';

import React from 'react';
import { User } from 'lucide-react';
import { popularHairstyles } from '@/data/mockBarbers';

export default function PopularHairstyles() {
  return (
    <div className="px-4 pb-6">
      <div className="flex space-x-3 md:space-x-4 overflow-x-auto scrollbar-hide horizontal-scroll">
        {popularHairstyles.map((style) => (
          <div key={style.id} className="flex flex-col items-center space-y-2 flex-shrink-0">
            {/* Circular Avatar - Responsive sizing */}
            <div className="w-22 h-22 md:w-30 md:h-30 lg:w-40 lg:h-40 rounded-full bg-gray-100 border-2 border-gray-200 flex items-center justify-center hover:border-gray-300 transition-colors cursor-pointer">
              {style.image ? (
                <img
                  src={style.image}
                  alt={style.name}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <User className="w-16 h-16 md:w-18 md:h-18 text-gray-400" />
              )}
            </div>

            {/* Style Name */}
            <span className="text-xs md:text-sm text-gray-600 text-center font-medium whitespace-nowrap max-w-[60px] md:max-w-[80px] truncate">
              {style.name}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}
