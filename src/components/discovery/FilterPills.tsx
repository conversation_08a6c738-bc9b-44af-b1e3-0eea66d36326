'use client';

import React from 'react';
import { ChevronDown } from 'lucide-react';

interface FilterPillsProps {
  selectedFilters: string[];
  onFiltersChange: (filters: string[]) => void;
}

const filterOptions = [
  { id: 'location', label: 'Location', hasDropdown: true },
  { id: 'price', label: 'Price Range', hasDropdown: true },
  { id: 'availability', label: 'Available Now', hasDropdown: false },
  { id: 'rating', label: 'Top Rated', hasDropdown: false },
  { id: 'services', label: 'Services', hasDropdown: true },
  { id: 'experience', label: 'Experience', hasDropdown: true },
  { id: 'styles', label: 'Styles', hasDropdown: true },
  { id: 'language', label: 'Language', hasDropdown: true },
];

export default function FilterPills({ selectedFilters, onFiltersChange }: FilterPillsProps) {
  const toggleFilter = (filterId: string) => {
    if (selectedFilters.includes(filterId)) {
      onFiltersChange(selectedFilters.filter(id => id !== filterId));
    } else {
      onFiltersChange([...selectedFilters, filterId]);
    }
  };

  return (
    <div className="px-4 pb-6">
      <div className="flex space-x-3 overflow-x-auto scrollbar-hide">
        {filterOptions.map((filter) => (
          <button
            key={filter.id}
            onClick={() => toggleFilter(filter.id)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-full border whitespace-nowrap transition-colors ${
              selectedFilters.includes(filter.id)
                ? 'bg-black text-white border-black'
                : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
            }`}
          >
            <span className="text-sm font-medium">{filter.label}</span>
            {filter.hasDropdown && (
              <ChevronDown className={`h-4 w-4 transition-colors ${
                selectedFilters.includes(filter.id) ? 'text-white' : 'text-gray-500'
              }`} />
            )}
          </button>
        ))}
      </div>
    </div>
  );
}
