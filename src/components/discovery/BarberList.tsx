'use client';

import React from 'react';
import { MapPin, Star, User } from 'lucide-react';
import { mockBarbers } from '@/data/mockBarbers';

export default function BarberList() {
  // Show different barbers for list view (starting from index 3)
  const listBarbers = mockBarbers.slice(3);

  return (
    <div className="px-4">
      {/* Section Title */}
      <div className="mb-4">
        <h2 className="text-lg font-medium text-gray-900">More Options</h2>
      </div>

      {/* Responsive Grid - Single column on mobile, 2 columns on tablet, 3 on desktop */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
      {listBarbers.map((barber) => (
        <div key={barber.id} className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
          <div className="flex h-full">
            {/* Image Container - Responsive sizing */}
            <div className="relative w-24 h-auto md:w-28 md:h-auto lg:w-32 lg:h-auto bg-gray-100 flex-shrink-0">
              <div className="absolute top-2 left-2 z-10">
                <span className="bg-black text-white text-xs font-medium px-2 py-1 rounded">
                  5 min
                </span>
              </div>
              
              {barber.image ? (
                <img 
                  src={barber.image} 
                  alt={barber.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <User className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 text-gray-300" />
                </div>
              )}
            </div>

            {/* Content - Responsive padding and sizing */}
            <div className="flex-1 p-4 md:p-5 lg:p-6">
              <div className="flex flex-col md:flex-row md:justify-between md:items-start h-full">
                <div className="flex-1 mb-3 md:mb-0">
                  {/* Barber Name */}
                  <h3 className="font-medium text-gray-900 mb-1 md:mb-2 text-base md:text-lg">{barber.name}</h3>
                  
                  {/* Location */}
                  <div className="flex items-center text-sm md:text-base text-gray-500 mb-2 md:mb-3">
                    <MapPin className="w-4 h-4 md:w-5 md:h-5 mr-1" />
                    <span>{barber.location}</span>
                  </div>
                  
                  {/* Distance and Rating */}
                  <div className="flex items-center space-x-3 md:space-x-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs md:text-sm">{barber.distance}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs md:text-sm flex items-center">
                        <Star className="w-3 h-3 md:w-4 md:h-4 text-yellow-400 fill-current mr-1" />
                        {barber.rating}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Book Now Button */}
                <button className="bg-black text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors">
                  Book Now
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
      </div>
    </div>
  );
}
