'use client';

import React, { useState } from 'react';
import SearchHeader from './SearchHeader';
import FilterPills from './FilterPills';
import PopularHairstyles from './PopularHairstyles';
import BarberGrid from './BarberGrid';
import Barber<PERSON>ist from './BarberList';

export default function DiscoveryPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

  return (
    <div className="min-h-screen bg-white">
      {/* Header with logo and message icon */}
      <div className="flex items-center justify-between p-4 pb-2">
        <div className="flex items-center space-x-2">
          {/* Logo - stylized cursive text like in wireframe */}
          <h1 className="text-2xl font-light tracking-wider text-black italic" style={{ fontFamily: 'cursive' }}>
            discover
          </h1>
        </div>
        <button className="p-2 rounded-full border border-gray-300 hover:bg-gray-50">
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </button>
      </div>

      {/* Location text */}
      <div className="px-4 pb-4">
        <p className="text-sm text-gray-500 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          Current Location
        </p>
      </div>

      {/* Search Header */}
      <SearchHeader 
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
      />

      {/* Filter Pills */}
      <FilterPills
        selectedFilters={selectedFilters}
        onFiltersChange={setSelectedFilters}
      />

      {/* Nearby Barbers - Horizontal Scroll Cards */}
      <BarberGrid />

      {/* Popular Hairstyles - Story Cards */}
      <PopularHairstyles />
      
      {/* More Options - List View */}
      <BarberList />

      {/* Bottom padding for mobile scroll */}
      <div className="h-20"></div>
    </div>
  );
}
