import React from 'react';
import { LucideIcon } from 'lucide-react';

interface ComingSoonCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
}

export default function ComingSoonCard({ icon: Icon, title, description }: ComingSoonCardProps) {
  return (
    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
      <div className="flex items-center space-x-3">
        <Icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
        <div>
          <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100">
            {title}
          </h3>
          <p className="text-blue-700 dark:text-blue-300 mt-1">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
}
