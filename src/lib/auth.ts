// Placeholder authentication utilities
// TODO: Replace with real authentication logic (JWT, sessions, etc.)

export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'super_admin';
}

// Mock admin user for development
const MOCK_ADMIN: AdminUser = {
  id: '1',
  email: '<EMAIL>',
  name: 'Admin User',
  role: 'admin'
};

// Mock credentials for development
const MOCK_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

/**
 * Placeholder function to check if admin is authenticated
 * In production, this would check JWT tokens, sessions, etc.
 */
export function isAdminAuthenticated(): boolean {
  if (typeof window === 'undefined') {
    return false; // Server-side, assume not authenticated
  }
  
  // Check localStorage for mock auth token
  const authToken = localStorage.getItem('admin_auth_token');
  return authToken === 'mock_admin_token';
}

/**
 * Get current admin user
 * In production, this would decode JWT or fetch from API
 */
export function getCurrentAdminUser(): AdminUser | null {
  if (!isAdminAuthenticated()) {
    return null;
  }
  
  return MOCK_ADMIN;
}

/**
 * Mock login function
 * In production, this would make API call to authenticate
 */
export async function loginAdmin(email: string, password: string): Promise<{
  success: boolean;
  user?: AdminUser;
  error?: string;
}> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  if (email === MOCK_CREDENTIALS.email && password === MOCK_CREDENTIALS.password) {
    // Set mock auth token
    localStorage.setItem('admin_auth_token', 'mock_admin_token');
    localStorage.setItem('admin_user', JSON.stringify(MOCK_ADMIN));
    
    return {
      success: true,
      user: MOCK_ADMIN
    };
  }
  
  return {
    success: false,
    error: 'Invalid email or password'
  };
}

/**
 * Mock logout function
 */
export function logoutAdmin(): void {
  localStorage.removeItem('admin_auth_token');
  localStorage.removeItem('admin_user');
}

/**
 * Redirect to login if not authenticated
 */
export function requireAdminAuth(): void {
  if (typeof window !== 'undefined' && !isAdminAuthenticated()) {
    window.location.href = '/admin/login';
  }
}

/**
 * Check if user has specific admin role
 */
export function hasAdminRole(requiredRole: 'admin' | 'super_admin'): boolean {
  const user = getCurrentAdminUser();
  if (!user) return false;
  
  if (requiredRole === 'admin') {
    return user.role === 'admin' || user.role === 'super_admin';
  }
  
  return user.role === requiredRole;
}
