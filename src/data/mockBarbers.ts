export interface Barber {
  id: number;
  name: string;
  shop: string;
  distance: string;
  distanceKm: number;
  rating: number;
  reviews: number;
  image: string | null;
  isNew: boolean;
  location: string;
  price: string;
  availability: string;
  services: string[];
  experience: number;
  languages: string[];
  styles: string[];
  childrenFriendly: boolean;
}

export const mockBarbers: <PERSON>[] = [
  {
    id: 1,
    name: '<PERSON>',
    shop: 'Classic Cuts',
    distance: '16 km away',
    distanceKm: 16,
    rating: 4.8,
    reviews: 124,
    image: null,
    isNew: true,
    location: 'Downtown Location',
    price: '$25-45',
    availability: 'Available today',
    services: ['Haircut', 'Beard Trim', 'Styling'],
    experience: 8,
    languages: ['English', 'Spanish'],
    styles: ['Classic', 'Modern'],
    childrenFriendly: true
  },
  {
    id: 2,
    name: '<PERSON>',
    shop: 'Modern Styles',
    distance: '4.8 km away',
    distanceKm: 4.8,
    rating: 4.9,
    reviews: 89,
    image: null,
    isNew: true,
    location: 'Midtown Location',
    price: '$30-50',
    availability: 'Available today',
    services: ['Haircut', 'Fade', 'Styling', 'Wash'],
    experience: 5,
    languages: ['English'],
    styles: ['Modern', 'Trendy'],
    childrenFriendly: false
  },
  {
    id: 3,
    name: '<PERSON>',
    shop: 'Vintage Barber',
    distance: '2.2 km away',
    distanceKm: 2.2,
    rating: 4.7,
    reviews: 156,
    image: null,
    isNew: true,
    location: 'Uptown Location',
    price: '$20-40',
    availability: 'Available today',
    services: ['Haircut', 'Beard Trim', 'Hot Towel'],
    experience: 12,
    languages: ['English', 'French'],
    styles: ['Vintage', 'Classic'],
    childrenFriendly: true
  },
  {
    id: 4,
    name: 'Chris Wilson',
    shop: 'Elite Cuts',
    distance: '12.5 km away',
    distanceKm: 12.5,
    rating: 4.6,
    reviews: 203,
    image: null,
    isNew: false,
    location: 'Westside Location',
    price: '$35-60',
    availability: 'Available tomorrow',
    services: ['Haircut', 'Styling', 'Color'],
    experience: 15,
    languages: ['English', 'Italian'],
    styles: ['Modern', 'Creative'],
    childrenFriendly: false
  },
  {
    id: 5,
    name: 'David Brown',
    shop: 'Quick Cuts',
    distance: '8.1 km away',
    distanceKm: 8.1,
    rating: 4.5,
    reviews: 67,
    image: null,
    isNew: false,
    location: 'Eastside Location',
    price: '$15-30',
    availability: 'Available now',
    services: ['Haircut', 'Beard Trim'],
    experience: 3,
    languages: ['English'],
    styles: ['Basic', 'Quick'],
    childrenFriendly: true
  },
  {
    id: 6,
    name: 'Robert Garcia',
    shop: 'Premium Barber',
    distance: '6.3 km away',
    distanceKm: 6.3,
    rating: 4.9,
    reviews: 298,
    image: null,
    isNew: false,
    location: 'Central Location',
    price: '$40-70',
    availability: 'Available today',
    services: ['Haircut', 'Beard Trim', 'Styling', 'Hot Towel', 'Massage'],
    experience: 20,
    languages: ['English', 'Spanish', 'Portuguese'],
    styles: ['Premium', 'Classic', 'Modern'],
    childrenFriendly: true
  }
];

export const popularHairstyles = [
  { id: 1, name: 'Classic Cut', image: null },
  { id: 2, name: 'Fade', image: null },
  { id: 3, name: 'Beard Trim', image: null },
  { id: 4, name: 'Modern Style', image: null },
  { id: 5, name: 'Vintage', image: null },
  { id: 6, name: 'Buzz Cut', image: null },
  { id: 7, name: 'Pompadour', image: null },
];
