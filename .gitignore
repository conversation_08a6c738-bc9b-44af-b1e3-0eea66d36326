# Dependencies
/node_modules/
/.pnp
.pnp.js

# Next.js build output
/.next/
/out/

# TypeScript cache (if you're using TypeScript)
/.turbo/
/*.tsbuildinfo

# Log files
*.log

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# VSCode directory
.vscode/

# macOS specific files
.DS_Store

# Windows specific files
Thumbs.db
ehthumbs.db

# npm package lock and yarn lock
/package-lock.json
/yarn.lock

# Next.js generated files for local development
/.next/
/out/

# JetBrains IDEs
.idea/

# Temporary files from editor
*~
*.swp
*.swo

# Serverless framework
.serverless/

# Test coverage
coverage/

# React Native
.expo/

# Firebase-related
.firebaserc
firebase-debug.log

# Storybook
/storybook-static/

# Next.js & React Native Debugging
.next/

# Cache directory for SWC compiler (for Next.js)
.swc/

# MacOS Finder metadata
.AppleDouble

# SvelteKit (if used within the project)
.svelte-kit/

# Next.js export (optional if using static export)
/out/
